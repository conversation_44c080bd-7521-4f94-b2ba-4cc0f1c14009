<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <title>Eisqr - ESG Platform</title>

    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0"
    />
    <meta name="mobile-web-app-capable" content="yes" />
    <meta name="description" content="Eisqr - ESG Platform" />
    <link rel="icon" type="image/x-icon" href="%PUBLIC_URL%/favicon.ico" />
    <link
      id="theme-link"
      rel="stylesheet"
      href="%PUBLIC_URL%/assets/themes/lara-light-indigo/theme.css"
    />
    <!-- <script src="https://maps.googleapis.com/maps/api/js?key=AIzaSyBAfSU3pX76vkQSAyo9YopnWBfN_CTTEd4" crossorigin="anonymous"></script> -->
    <!-- <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Material+Icons|Material+Icons+Outlined|Material+Icons+Two+Tone|Material+Icons+Round|Material+Icons+Sharp" crossorigin="anonymous"> -->
    <link
      href="https://fonts.googleapis.com/css?family=Material+Icons|Material+Icons+Outlined|Material+Symbols+Outlined|Material+Icons+Two+Tone|Material+Icons+Round|Material+Icons+Sharp"
      rel="stylesheet"
    />

    <script
      type="text/javascript"
      src="https://maps.googleapis.com/maps/api/js?key=AIzaSyBAfSU3pX76vkQSAyo9YopnWBfN_CTTEd4&libraries=places"
    ></script>
    <!-- <script
      type="text/javascript"
      src="https://www.gstatic.com/charts/loader.js"
    ></script>  -->
   <script
      class="cytrio-script"
      src="https://cytriocpmprod.blob.core.windows.net/cytrio-public/cookiescript/2532/3027/script.js"
    ></script> 

    <style>
      .swal2-container {
        z-index: 9999 !important;
      }

      /* .ql-link {
                display: none !important;
            } */

      .ql-clean {
        display: none !important;
      }

      .p-datatable-wrapper {
        min-height: 150px;
        max-height: 350px;
      }
      .ql-code-block {
        display: none !important;
      }
      .text-editor .ql-toolbar .ql-formats .ql-link {
        display: block !important;
      }
      .text-editor .ql-toolbar .ql-formats .ql-image {
        display: none !important;
      }

      .text-area .ql-toolbar .ql-formats .ql-header {
        display: none;
      }
      .text-area .ql-toolbar .ql-formats .ql-font {
        display: none !important;
      }
      .text-area .ql-toolbar .ql-formats .ql-link {
        display: block !important;
      }
      .text-area .ql-toolbar .ql-formats .ql-image {
        display: none !important;
      }
      .target-options .ql-toolbar .ql-formats .ql-header {
        display: none !important;
      }

      .target-options .ql-toolbar .ql-formats .ql-font {
        display: none !important;
      }
      .target-options .ql-toolbar .ql-formats .ql-link {
        display: none !important;
      }
      .nasdaq sectionheader1 {
        color: #005284;
        font-weight: 700;
        text-decoration: underline;
      }
      .gri sectionheader1 {
        color: #005284;
        font-weight: 700;
      }
      .gri .list {
        color: black;
        list-style-type: none;
        padding-right: 30px;
        text-align: justify;
      }

      .list label {
        display: block;
        margin-left: 30px;
        margin-bottom: 5px;
      }
      .draggable-widget {
        margin: 5px;
        padding: 5px;
        border: 1px solid grey;
        border-radius: 5px;
      }

      .target-options .ql-toolbar .ql-formats .ql-image {
        display: none !important;
      }

      .target-options .ql-toolbar .ql-formats .ql-code-block {
        display: none !important;
      }

      .p-component .p-editor-container {
        margin-top: 10px;
      }

      .stickyToTopTableHeaders {
        position: sticky;
        background-color: #fff;
        z-index: 1100;
      }

      .frozen-header-table {
        position: sticky;
        top: 0;
        z-index: 1;
      }

      .table_63 {
        height: 63vh;
      }

      .hvef {
        height: 69vh !important;
        overflow: auto !important;
      }

      .p-datatable .p-datatable-footer {
        background: white !important;
        border: 0 !important;
        display: flex;
        justify-content: center;
      }
      .fullheight .p-datatable-wrapper {
        height: 100%;
      }
      .headercenter .p-column-header-content .p-column-title {
        width: 100%;
      }
      /* 
        .p-datatable-thead {
            position: absolute;
            width: inherit ;
            z-index: 9;
        } */
      .gchart {
        width: 100%;
        height: 0;
        padding-bottom: 60%;
        /* Adjust the aspect ratio as needed */
        position: relative;
      }

      .vertical-line {
        border: none;
        border-left: 2px solid black;
        /* Customize the line color and width */
        height: auto;
        /* Customize the line height */
        margin: 0 10px;
        /* Customize the margin around the line */
      }

      .gchart > div {
        position: absolute;
        width: 100% !important;
        height: 100% !important;
      }
      td:has(> .cell-p-invalid) {
        border: 1px solid red !important;
      }
      td:has(> .cell-p-invalid-id) {
        border: 1px dashed red !important;
      }
      .chip-fw ul {
        width: 100%;
      }
      .actionbtn .pi {
        font-size: 12px;
      }
      .tag-tooltip .p-tooltip-text {
        background: white;
      }

      .form-group label {
        color: #005284 !important;
        /*you can change the color */
      }

      .form-wrap.form-builder .stage-wrap.empty {
        border: 3px dashed #005284 !important;
      }

      .form-wrap.form-builder .frmb-control li {
        box-shadow: inset 0 0 0 1px #005284 !important;
        overflow-x: scroll !important;
      }

      .my-custom-view-wrapper {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        transition: "background 0.2s ease";
        min-height: 28px;
        color: black;
        background: white;
        display: flex;
        align-items: center;
      }

      .my-custom-view-wrapper-selected {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        transition: "background 0.2s ease";
        min-height: 28px;
        background: gray;
        color: white;
        display: flex;
        align-items: center;
      }

      .p-multiselect-header {
        display: none !important;
      }

      .hidefilter .p-multiselect-header {
        display: flex !important;
      }

      .custom-edit-button {
        visibility: hidden;
      }

      /* hide list controls  */
      .pac-container {
        z-index: 9999;
      }

      .autocomplete_width {
        width: 100%;
      }
    </style>
  </head>

  <body class="sidebar-icon-only font-lato">
    <noscript> You need to enable JavaScript to run Eisqr Platform. </noscript>
    <div id="root" class="font-lato">
      <div class="splash-screen">
        <div class="splash-container">
          <div class="splash-double-bounce1"></div>
          <div class="splash-double-bounce2"></div>
        </div>
      </div>
    </div>
  </body>

</html>
