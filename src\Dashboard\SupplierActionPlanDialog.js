import React, { useState, useEffect } from 'react';
import { Dialog } from 'primereact/dialog';
import { Accordion, AccordionTab } from 'primereact/accordion';
import { Calendar } from 'primereact/calendar';
import { But<PERSON> } from 'primereact/button';
import { InputTextarea } from 'primereact/inputtextarea';
import { format } from 'date-fns';
import APIServices from '../service/APIService';
import { API } from '../constants/api_url';
import Swal from 'sweetalert2';
import { DateTime } from 'luxon';
import { useSelector } from 'react-redux';

export default function SupplierActionPlanDialog({ visible, onHide, data, refresh }) {
    const [actionData, setActionData] = useState([]);
    const [loading, setLoading] = useState(false);
    const [comments, setComments] = useState([]);
    const login_data = useSelector((state) => state.user.userdetail);
    const admin_data = useSelector((state) => state.user.admindetail);
    const userList = useSelector((state) => state.userlist.userList);

    useEffect(() => {
        if (data?.actions?.length) {
            const initialized = data.actions.map(action => ({
                ...action,
                rootCause: action.rootCause || '',
                proposedCorrectiveAction: action.proposedCorrectiveAction || '',
                actionTargetDate: action.actionTargetDate ? new Date(action.actionTargetDate) : null,
                actionPlanApproverComments: action.actionPlanApproverComments || ''
            }));
            setActionData(initialized);

            // Initialize comments array with empty strings for each action
            setComments(new Array(data.actions.length).fill(''));
        }
    }, [data]);
        const userLookup = userList.reduce((acc, user) => {
        acc[user.id] = user;
        return acc;
    }, {});
    const getUser = (id) => {
        if (id === admin_data.id) {
            return "Enterprise Admin";
        }
        console.log(userLookup[id])
        return userLookup[id] ? userLookup[id].information.empname : ''
    };
    const isSaveEnabled = actionData.every(
        act =>
            act.rootCause?.trim() &&
            act.proposedCorrectiveAction?.trim() &&
            act.actionTargetDate
    );

    const handleChange = (index, field, value) => {
        const updated = [...actionData];
        updated[index][field] = value;
        setActionData(updated);
    };

    const handleSave = () => {
        if (isSaveEnabled) {
            setLoading(true);
            console.log(actionData);
            APIServices.post(API.SubmitActionPlan, actionData)
                .then(() => {
                    setLoading(false);
                    Swal.fire({
                        icon: 'success',
                        title: 'Action Plan Submitted',
                        text: 'Your action plan has been submitted successfully.'
                    });
                    refresh();
                    onHide();
                })
                .catch(error => {
                    setLoading(false);
                    Swal.fire({
                        icon: 'error',
                        title: 'Error',
                        text: 'Failed to submit action plan. Please try again.'
                    });
                    console.error(error);
                });
        }
    };

    const handleCommentChange = (index, value) => {
        const newComments = [...comments];
        newComments[index] = value;
        setComments(newComments);

        // Update the actionData with the new comment
        const updatedActionData = [...actionData];
        updatedActionData[index].actionPlanApproverComments = value;
        setActionData(updatedActionData);
    };

    const validateComments = () => {
        // For return action, at least one action should have a comment
        return comments.some(comment => comment.trim() !== '');
    };

    const handleApprove = () => {
        setLoading(true);

        // Update each action with approval properties
        const updatedActions = actionData.map(action => ({
            ...action,
            actionPlanApprovedBy: login_data.id,
            actionPlanApprovedOn: DateTime.utc().toISO(),
            actionPlanApproverComments: action.actionPlanApproverComments || ''
        }));



        APIServices.post(API.ApproveActionPlan, updatedActions)
            .then(() => {
                setLoading(false);
                Swal.fire({
                    icon: 'success',
                    title: 'Action Plan Approved',
                    text: 'The action plan has been approved successfully.'
                });
                refresh();
                onHide();
            })
            .catch(error => {
                setLoading(false);
                Swal.fire({
                    icon: 'error',
                    title: 'Error',
                    text: 'Failed to approve action plan. Please try again.'
                });
                console.error(error);
            });
    };

    const handleReturn = () => {
        // Validate that at least one action has a comment
        if (!validateComments()) {
            Swal.fire({
                icon: 'error',
                title: 'Comments Required',
                text: 'Please provide comments for at least one action before returning.'
            });
            return;
        }

        // Update each action with return properties
        const updatedActions = actionData.map(action => ({
            ...action,
            actionPlanRejectedBy: login_data.id,
            actionPlanRejectedOn: DateTime.utc().toISO()
        }));

        // Create payload with updated actions and action plan type
        const payload = {
            actions: updatedActions,
            actionPlanId: data.id,
            actionPlanType: 21 // Keep as 21 for returned action plans
        };

        setLoading(true);
        APIServices.post(API.RejectActionPlan, updatedActions)
            .then(() => {
                setLoading(false);
                Swal.fire({
                    icon: 'success',
                    title: 'Action Plan Returned',
                    text: 'The action plan has been returned with feedback.'
                });
                refresh();
                onHide();
            })
            .catch(error => {
                setLoading(false);
                Swal.fire({
                    icon: 'error',
                    title: 'Error',
                    text: 'Failed to return action plan. Please try again.'
                });
                console.error(error);
            });
    };

    return (
        <Dialog header="Action Details" visible={visible} onHide={onHide} style={{ width: '80vw' }}>
            <div className="p-3">
                <div className="mb-3">
                    <strong>MSI ID:</strong> {data?.msiId} <br />
                    <strong>{data?.statusCode === 6 ? 'Submitted on' : 'Approved on'}:</strong> {data?.submittedDate ? DateTime.fromISO(data?.submittedDate, { zone: 'utc' }).toLocal().toFormat('dd-LLL-yyyy') : 'N/A'} <br />
                    {data?.statusCode === 0 && (
                        <div className="mt-1">
                            <span className="badge badge-success p-1">
                                <i className="pi pi-check-circle mr-1"></i> Approved
                            </span>
                        </div>
                    )}
                </div>

                <Accordion multiple activeIndex={[0]}>
                    {actionData.map((action, index) => (
                        <AccordionTab key={index} header={`#${action.actionId} - ${action.finding}`}>
                            <div className="mb-3">
                                <label><strong>Root Cause:</strong></label>
                                <div dangerouslySetInnerHTML={{ __html: action.rootCause }} />
                            </div>

                            <div className="mb-3">
                                <label><strong>Proposed Corrective Action(s):</strong></label>
                                <div dangerouslySetInnerHTML={{ __html: action.proposedCorrectiveAction }} />
                            </div>

                            <div className="mb-3">
                                <label><strong>Target Completion Date:</strong></label><br />
                                <div>{action.actionTargetDate ? format(new Date(action.actionTargetDate), 'dd-LLL-yyyy') : 'N/A'}</div>
                            </div>

                            {/* For active action plans that haven't been approved/rejected yet, show editable textarea */}
                            {(data?.statusCode === 6 && !action.actionPlanApprovedOn && !action.actionPlanRejectedOn) && (
                                <div >
                                    <label style={{marginBottom:10}}><strong>Comments:</strong></label><br />
                                    <InputTextarea
                                        value={action.actionPlanApproverComments}
                                        onChange={(e) => handleCommentChange(index, e.target.value)}
                                        rows={3}
                                        style={{ width: '100%' }}
                                        placeholder="Enter your comments here..."
                                    />
                                </div>
                            )}

                            {/* Only show comments section if comments exist */}
                            {((data?.statusCode === 0 || action.actionPlanApprovedOn || action.actionPlanRejectedOn) && action.actionPlanApproverComments) && (
                                <div >
                                    <label classname='flex' style={{marginBottom:10}}><strong>Comments:</strong></label><br />
                                    <div className="p-3">
                                        <div>{action.actionPlanApproverComments}</div>
                                    </div>
                                </div>
                            )}

                
                        </AccordionTab>
                    ))}
                </Accordion>

                <div className="mt-4 text-right">
                    {actionData.every((x) => x.type === 12) && isSaveEnabled && (
                        <Button label="Save & Submit Action Plan" icon="pi pi-check" onClick={handleSave} className="mr-2" loading={loading} />
                    )}

                    {/* Show approve/return buttons only for action plans that are under review (statusCode = 6) */}
                    {data?.statusCode === 6 && !actionData.some(action => action.actionPlanApprovedOn || action.actionPlanRejectedOn) && (
                        <>
                            <Button
                                label="Approve"
                                icon="pi pi-check-circle"
                                className="p-button-success mr-2"
                                onClick={handleApprove}
                                loading={loading}
                            />
                            <Button
                                label="Return"
                                icon="pi pi-times-circle"
                                className="p-button-danger"
                                onClick={handleReturn}
                                loading={loading}
                                disabled={!validateComments()}
                            />
                        </>
                    )}

                    {/* Show status message for approved action plans (statusCode = 0) */}
                    {data?.statusCode === 0 && (
                        <div className="text-success">
                            <i className="pi pi-check-circle mr-2"></i>
                            This action plan has been approved
                        </div>
                    )}

                    {/* Show status messages based on action properties */}
                    {data?.statusCode === 6 && actionData.some(action => action.actionPlanApprovedOn) && (
                        <div className="text-success">
                            <i className="pi pi-check-circle mr-2"></i>
                            This action plan has been approved
                        </div>
                    )}

                    {data?.statusCode === 6 && actionData.some(action => action.actionPlanRejectedOn) && (
                        <div className="text-danger">
                            <i className="pi pi-times-circle mr-2"></i>
                            This action plan has been returned
                        </div>
                    )}
                </div>
            </div>
        </Dialog>
    );
}
